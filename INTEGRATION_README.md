# ImpressCMS + Laravel Integration

This project integrates ImpressCMS within a Laravel application, allowing both systems to coexist and work together seamlessly.

## How It Works

The integration follows these principles:

1. **File-First Routing**: When a request comes in, the system first checks if the requested file exists physically in the `/legacy/` folder
2. **Legacy Controller**: If a file exists in the legacy folder, it's served through <PERSON><PERSON>'s `LegacyController`
3. **Lara<PERSON> Fallback**: If no file exists in the legacy folder, <PERSON><PERSON>'s normal routing system takes over
4. **Shared Environment**: Both systems can share database connections and environment variables

## Directory Structure

```
/
├── app/                          # Laravel application files
│   ├── Http/
│   │   ├── Controllers/
│   │   │   └── LegacyController.php    # Handles legacy file serving
│   │   └── Middleware/
│   │       └── LegacyFileMiddleware.php # Checks for legacy files
│   ├── Exceptions/
│   │   ├── LegacyExitScript.php        # Handles legacy exit() calls
│   │   └── LegacyView.php              # Handles Laravel views from legacy
│   └── helpers/
│       └── legacy.php                  # Helper functions for legacy integration
├── legacy/                       # ImpressCMS files (formerly htdocs/)
│   ├── index.php                # ImpressCMS main entry point
│   ├── mainfile.php             # ImpressCMS configuration
│   ├── admin.php                # ImpressCMS admin
│   ├── user.php                 # ImpressCMS user management
│   ├── modules/                 # ImpressCMS modules
│   ├── themes/                  # ImpressCMS themes
│   └── ...                      # Other ImpressCMS files
├── public/                       # Laravel public directory
├── routes/                       # Laravel routes
└── ...                          # Other Laravel files
```

## Key Components

### LegacyController
- Serves files from the `/legacy/` directory
- Sets up proper environment variables for ImpressCMS
- Handles output buffering to capture legacy output
- Provides security checks to prevent directory traversal

### LegacyFileMiddleware
- Checks if requested files exist in the legacy directory
- Handles directory requests with index.php files
- Skips Laravel-specific routes (like `/laravel-test`)

### Helper Functions
- `legacy_path($path)`: Get path to legacy files
- `legacy_view($view, $data)`: Render Laravel views from legacy code
- `legacy_exit()`: Exit from legacy code without stopping PHP

## Configuration

### ImpressCMS Configuration (legacy/mainfile.php)
The mainfile.php has been updated to:
- Use relative paths for the legacy directory
- Read database configuration from Laravel's .env file
- Set proper URL paths for the legacy subfolder

### Laravel Configuration
- Middleware registered in `bootstrap/app.php`
- Routes configured in `routes/web.php`
- Helper functions autoloaded via `composer.json`

## Testing the Integration

1. **Legacy Files**: Visit `/test.php` to see a legacy PHP file served through Laravel
2. **Laravel Routes**: Visit `/laravel-test` to see Laravel's JSON API response
3. **Welcome Page**: Visit `/welcome.php` for integration status and links
4. **ImpressCMS**: Visit `/user.php` or `/admin.php` for ImpressCMS functionality

## Environment Variables

Add these to your `.env` file for ImpressCMS database configuration:

```env
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=your_database
DB_USERNAME=your_username
DB_PASSWORD=your_password
```

## Installation Steps

1. **Move ImpressCMS**: The htdocs content has been moved to `/legacy/`
2. **Install Laravel**: Laravel application structure is in the root
3. **Configure Database**: Update `.env` with database credentials
4. **Install Dependencies**: Run `composer install`
5. **Test Integration**: Visit the application to verify both systems work

## Next Steps

1. **Install ImpressCMS**: Run the ImpressCMS installer if not already installed
2. **Configure URLs**: Update ImpressCMS configuration for the new URL structure
3. **Test Functionality**: Verify all ImpressCMS features work correctly
4. **Gradual Migration**: Start moving features from ImpressCMS to Laravel as needed

## Security Considerations

- Directory traversal protection in LegacyController
- Input validation for legacy requests
- Proper session handling between systems
- CSRF protection for new Laravel routes

## Troubleshooting

### Common Issues

1. **File Not Found**: Check if the file exists in `/legacy/` directory
2. **Database Errors**: Verify `.env` database configuration
3. **Path Issues**: Ensure ImpressCMS paths are correctly configured
4. **Permission Errors**: Check file permissions on legacy directory

### Debug Mode

Enable Laravel debug mode in `.env`:
```env
APP_DEBUG=true
```

This will show detailed error messages for troubleshooting.
