<h4><{$lang_activenotifications}></h4>

<p><a href="<{$icms_url}>/userinfo.php?uid=<{$user.uid}>" title="<{$user.name}>"><{$smarty.const._PROFILE}></a>&nbsp;<span style="font-weight:bold;">&raquo;&raquo;</span>&nbsp;<{$smarty.const._MB_SYSTEM_NOTIF}> </p>
<form id="notificationlist" action="notifications.php" method="post">
<table class="outer">
  <tr>
	<th><input name="allbox" id="allbox" onclick="xoopsCheckAll('notificationlist', 'allbox');" type="checkbox" value="<{$lang_checkall}>" /></th>
    <th><{$lang_event}></th>
    <th><{$lang_category}></th>
    <th><{$lang_itemid}></th>
    <th><{$lang_itemname}></th>
  </tr>
  <{foreach item=module from=$modules}>
  <tr>
    <td class="head"><input name="del_mod[<{$module.id}>]" id="del_mod[]" onclick="xoopsCheckGroup('notificationlist', 'del_mod[<{$module.id}>]', 'del_not[<{$module.id}>][]');" type="checkbox" value="<{$module.id}>" /></td>
    <td class="head" colspan="4"><{$lang_module}>: <{$module.name}></td>
  </tr>
  <{foreach item=category from=$module.categories}>
  <{foreach item=item from=$category.items}>
  <{foreach item=notification from=$item.notifications}>
  <tr>
    <{cycle values=odd,even assign=class}>
    <td class="<{$class}>"><input type="checkbox" name="del_not[<{$module.id}>][]" id="del_not[<{$module.id}>][]" value="<{$notification.id}>" /></td>
    <td class="<{$class}>"><{$notification.event_title}></td>
    <td class="<{$class}>"><{$notification.category_title}></td>
    <td class="<{$class}>"><{if $item.id != 0}><{$item.id}><{/if}></td>
    <td class="<{$class}>"><{if $item.id != 0}><{if $item.url != ''}><a href="<{$item.url}>" title="<{$item.name}>"><{/if}><{$item.name}><{if $item.url != ''}></a><{/if}><{/if}></td>
  </tr>
  <{/foreach}>
  <{/foreach}>
  <{/foreach}>
  <{/foreach}>
  <tr>
    <td class="foot" colspan="5">
      <input type="submit" name="delete_cancel" value="<{$lang_cancel}>" />
      <input type="reset" name="delete_reset" value="<{$lang_clear}>" />
      <input type="submit" name="delete" value="<{$lang_delete}>" />
      <input type="hidden" name="XOOPS_TOKEN_REQUEST" value="<{$notification_token}>" />
    </td>
  </tr>
</table>
</form>
