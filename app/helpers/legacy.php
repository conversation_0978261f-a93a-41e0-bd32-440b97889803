<?php

if (!function_exists('legacy_path')) {
    /**
     * Get the path to a file in the legacy directory
     *
     * @param string $path
     * @return string
     */
    function legacy_path($path = null)
    {
        return base_path('legacy/' . $path);
    }
}

if (!function_exists('legacy_view')) {
    /**
     * Render a Laravel view from legacy code
     *
     * @param string $view
     * @param array $data
     * @param array $mergeData
     * @throws \App\Exceptions\LegacyView
     */
    function legacy_view($view = null, $data = [], $mergeData = [])
    {
        throw new \App\Exceptions\LegacyView(
            view($view, $data, $mergeData)
        );
    }
}

if (!function_exists('legacy_exit')) {
    /**
     * Exit from legacy code without stopping PHP execution
     *
     * @throws \App\Exceptions\LegacyExitScript
     */
    function legacy_exit()
    {
        throw new \App\Exceptions\LegacyExitScript();
    }
}
