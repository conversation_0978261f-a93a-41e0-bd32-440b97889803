<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class LegacyFileMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $path = $request->getPathInfo();

        // Remove leading slash
        $path = ltrim($path, '/');

        // Skip Laravel-specific routes
        if (str_starts_with($path, 'laravel-') || str_starts_with($path, 'api/')) {
            return $next($request);
        }

        // If path is empty, check for index.php
        if (empty($path)) {
            $path = 'index.php';
        }

        // Build the full path to the legacy file
        $legacyFilePath = base_path('legacy/' . $path);

        // Check if the file exists in the legacy directory
        if (file_exists($legacyFilePath) && is_file($legacyFilePath)) {
            // File exists in legacy, continue to legacy controller
            return $next($request);
        }

        // If it's a directory, check for index.php in that directory
        $legacyDirPath = base_path('legacy/' . $path);
        if (is_dir($legacyDirPath)) {
            $indexPath = $legacyDirPath . '/index.php';
            if (file_exists($indexPath)) {
                // Set the legacy path for the controller
                $request->merge(['legacy_path' => $path . '/index.php']);
                return $next($request);
            }
        }

        // Check if it's a PHP file without extension
        if (!pathinfo($path, PATHINFO_EXTENSION)) {
            $phpFilePath = base_path('legacy/' . $path . '.php');
            if (file_exists($phpFilePath) && is_file($phpFilePath)) {
                $request->merge(['legacy_path' => $path . '.php']);
                return $next($request);
            }
        }

        // File doesn't exist in legacy, return 404 or let Laravel handle it
        // For now, let's continue to the controller which will handle the 404
        return $next($request);
    }
}
