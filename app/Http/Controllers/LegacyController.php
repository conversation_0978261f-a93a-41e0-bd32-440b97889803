<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\Exceptions\LegacyExitScript;
use App\Exceptions\LegacyView;

class LegacyController extends Controller
{
    /**
     * Handle legacy file requests
     */
    public function __invoke(Request $request, $path = null)
    {
        // If no path provided, get from request
        if ($path === null) {
            $path = $request->getPathInfo();
        }

        // Remove leading slash
        $path = ltrim($path, '/');

        // If path is empty, default to index.php
        if (empty($path)) {
            $path = 'index.php';
        }
        
        // Check if legacy_path was set by middleware
        if ($request->has('legacy_path')) {
            $path = $request->get('legacy_path');
        }
        
        // Build the full path to the legacy file
        $legacyFilePath = base_path('legacy/' . $path);
        
        // Verify the file exists and is within the legacy directory (security check)
        if (!file_exists($legacyFilePath) || !is_file($legacyFilePath)) {
            abort(404);
        }
        
        // Ensure the file is within the legacy directory (prevent directory traversal)
        $realLegacyPath = realpath(base_path('legacy'));
        $realFilePath = realpath($legacyFilePath);
        
        if (!$realFilePath || strpos($realFilePath, $realLegacyPath) !== 0) {
            abort(404);
        }
        
        try {
            // Set up the environment for ImpressCMS
            $this->setupLegacyEnvironment($request);
            
            // Start output buffering to capture the legacy output
            ob_start();
            
            // Change to the legacy directory
            $originalDir = getcwd();
            chdir(base_path('legacy'));
            
            // Include the legacy file
            include $legacyFilePath;
            
            // Restore original directory
            chdir($originalDir);
            
            // Get the output
            $output = ob_get_clean();
            
        } catch (LegacyExitScript $e) {
            // Handle legacy exit/die calls
            $output = ob_get_clean();
        } catch (LegacyView $e) {
            // Handle Laravel view rendering from legacy code
            return $e->getView();
        } catch (\Exception $e) {
            // Handle other exceptions
            ob_end_clean();
            throw $e;
        }
        
        // Determine content type based on file extension
        $contentType = $this->getContentType($path);
        
        return new Response($output, 200, [
            'Content-Type' => $contentType,
        ]);
    }
    
    /**
     * Set up the environment for legacy ImpressCMS execution
     */
    private function setupLegacyEnvironment(Request $request)
    {
        // Set up $_SERVER variables that ImpressCMS might expect
        $_SERVER['SCRIPT_NAME'] = '/' . ltrim($request->getPathInfo(), '/');
        $_SERVER['REQUEST_URI'] = $request->getRequestUri();
        $_SERVER['QUERY_STRING'] = $request->getQueryString() ?: '';
        
        // Set up $_GET, $_POST, $_REQUEST from Laravel request
        $_GET = $request->query->all();
        $_POST = $request->request->all();
        $_REQUEST = array_merge($_GET, $_POST);
        
        // Set up $_FILES if any
        $_FILES = [];
        foreach ($request->files->all() as $key => $file) {
            if (is_array($file)) {
                $_FILES[$key] = [];
                foreach ($file as $index => $f) {
                    $_FILES[$key]['name'][$index] = $f->getClientOriginalName();
                    $_FILES[$key]['type'][$index] = $f->getClientMimeType();
                    $_FILES[$key]['tmp_name'][$index] = $f->getPathname();
                    $_FILES[$key]['error'][$index] = $f->getError();
                    $_FILES[$key]['size'][$index] = $f->getSize();
                }
            } else {
                $_FILES[$key] = [
                    'name' => $file->getClientOriginalName(),
                    'type' => $file->getClientMimeType(),
                    'tmp_name' => $file->getPathname(),
                    'error' => $file->getError(),
                    'size' => $file->getSize(),
                ];
            }
        }
    }
    
    /**
     * Get content type based on file extension
     */
    private function getContentType($path)
    {
        $extension = pathinfo($path, PATHINFO_EXTENSION);
        
        switch (strtolower($extension)) {
            case 'css':
                return 'text/css';
            case 'js':
                return 'application/javascript';
            case 'png':
                return 'image/png';
            case 'jpg':
            case 'jpeg':
                return 'image/jpeg';
            case 'gif':
                return 'image/gif';
            case 'ico':
                return 'image/x-icon';
            case 'xml':
                return 'application/xml';
            case 'json':
                return 'application/json';
            default:
                return 'text/html; charset=UTF-8';
        }
    }
}
