<?php
// $Id: modulesadmin.php 11129 2011-03-29 00:57:50Z skenow $
//%%%%%%	File Name  modulesadmin.php 	%%%%%
define('_MD_AM_MODADMIN', 'Module beheer');
define('_MD_AM_MODULE', 'Module');
define('_MD_AM_VERSION', 'Versie');
define('_MD_AM_LASTUP', 'Laaste keer bijgewerkt');
define('_MD_AM_DEACTIVATED', 'Gedeactiveerd');
define('_MD_AM_ACTION', 'Actie');
define('_MD_AM_DEACTIVATE', 'Deactiveren');
define('_MD_AM_ACTIVATE', 'Activeren');
define('_MD_AM_UPDATE', 'Bijwerken');
define('_MD_AM_DUPEN', 'Dupliceer invoer in module tabel!');
define('_MD_AM_DEACTED', 'De geselecteerde module is gedeactiveerd en kan nu veilig worden gedeinstalleerd.');
define('_MD_AM_ACTED', 'De geselecteerde module is geactiveerd!');
define('_MD_AM_UPDTED', 'de geselecteerde module is bijgewerkt!');
define('_MD_AM_SYSNO', 'Systeem module kan niet worden gedeactiveerd.');
define('_MD_AM_STRTNO', 'Deze module is ingesteld als beginpagina. Verander de startmodule in de door u gewenste module.');
// added in RC2
define('_MD_AM_PCMFM', 'Bevestig alstublieft:');
// added in RC3
define('_MD_AM_ORDER', 'Volgorde');
define('_MD_AM_ORDER0', '(0 = verbergen)');
define('_MD_AM_ACTIVE', 'Actief');
define('_MD_AM_INACTIVE', 'Inactief');
define('_MD_AM_NOTINSTALLED', 'Niet geïnstalleerd');
define('_MD_AM_NOCHANGE', 'Geen wijzigingen');
define('_MD_AM_INSTALL', 'Installeren');
define('_MD_AM_UNINSTALL', 'Deïnstalleren');
define('_MD_AM_SUBMIT', 'Toevoegen');
define('_MD_AM_CANCEL', 'Annuleren');
define('_MD_AM_DBUPDATE', 'Database succesvol bijgewerkt!');
define('_MD_AM_BTOMADMIN', 'Terug naar de module beheerpagina');
// %s represents module name
define('_MD_AM_FAILINS', 'Niet mogelijk om %s te installeren.');
define('_MD_AM_FAILACT', 'Niet mogelijk om %s te activeren.');
define('_MD_AM_FAILDEACT', 'Niet mogelijk om %s te deactiveren.');
define('_MD_AM_FAILUNINS', 'Niet mogelijk om %s te deinstalleren.');
define('_MD_AM_FAILORDER', 'Niet mogelijk om %s te hergroeperen.');
define('_MD_AM_FAILWRITE', 'Niet mogelijk om het hoofdmenu te overschrijven.');
define('_MD_AM_ALEXISTS', 'Module %s bestaat al.');
define('_MD_AM_ERRORSC', 'Fout(en):');
define('_MD_AM_OKINS', 'Module %s succesvol geïnstalleerd.');
define('_MD_AM_OKACT', 'Module %s succesvol geactiveerd.');
define('_MD_AM_OKDEACT', 'Module %s succesvol gedeactiveerd.');
define('_MD_AM_OKUPD', 'Module %s succesvol bijgewerkt.');
define('_MD_AM_OKUNINS', 'Module %s succesvol gedeïnstalleerd.');
define('_MD_AM_OKORDER', 'Module %s succesvol gewijzigd.');
define('_MD_AM_RUSUREINS', 'Klik op onderstaande knop om deze module te installeren');
define('_MD_AM_RUSUREUPD', 'Klik op onderstaande knop om deze module bij te werken');
define('_MD_AM_RUSUREUNINS', 'Deze module definitief deinstalleren?');
define('_MD_AM_LISTUPBLKS', 'De volgende blokken worden bijgewerkt.<br />Selecteer de blokken waarvan de inhoud en intellingen mogen worden overschreven.<br />');
define('_MD_AM_NEWBLKS', 'Nieuwe blokken');
define('_MD_AM_DEPREBLKS', 'Afgekeurde blokken');
define('_MD_AM_MODULESADMIN_SUPPORT', 'Module Support Website');
define('_MD_AM_MODULESADMIN_STATUS', 'Status');
define('_MD_AM_MODULESADMIN_MODULENAME', 'Module Naam');
define('_MD_AM_MODULESADMIN_MODULETITLE', 'Module Titel');
######################## Added in 1.2 ###################################
define('_MD_AM_MOD_DATA_UPDATED', 'Module data bijgewerkt.');
define('_MD_AM_MOD_REBUILD_BLOCKS', 'Overnieuw opbouwen blokken...');
define('_MD_AM_INSTALLED', 'Geïnstalleerde modules');
define('_MD_AM_NONINSTALL', 'Niet geïnstalleerde modules');
define('_MD_AM_IMAGESFOLDER_UPDATE_TITLE', 'Afbeeldingen beheer map dient schrijfbaar te zijn');
define('_MD_AM_IMAGESFOLDER_UPDATE_TEXT', 'De nieuwe versie van Afbeeldingen beheer heeft de opslaglocatie van uw afbeeldingen gewijzigd. Deze update zal proberen uw afbeeldingen naar de juiste locatie te verplaatsen, dit vereist dat de opslagmap schrijfrechten heeft. Stel de juiste rechten in voordat u op de onderstaande Update knop klikt.<br /><b>Afbeeldingen beheer map</b>: %s');
define('_MD_AM_PLUGINSFOLDER_UPDATE_TITLE', 'Plugins/Preloads map dient schrijfbaar te zijn');
define('_MD_AM_PLUGINSFOLDER_UPDATE_TEXT', 'De nieuwe versie van Afbeeldingen beheer heeft de opslaglocatie van de preloads gewijzigd. Deze update zal proberen uw preloads naar de juiste locatie te verplaatsen, dit vereist dat de opslagmap schrijfrechten heeft. Stel de juiste rechten in voordat u op de onderstaande Update knop klikt.<br /><b>Plugins map</b>: %s<br /><b>Preloads map</b>: %s');
// Added and Changed in 1.3
define('_MD_AM_UPDATE_FAIL', 'Onmogelijk om %s te updaten.');
define('_MD_AM_FUNCT_EXEC', 'Functie <b>%s</b> is succesvol uitgevoerd.');
define('_MD_AM_FAIL_EXEC', 'Mislukt om <b>%s</b> uit te voeren.');
define('_MD_AM_INSTALLING', 'Installatie');
define('_MD_AM_SQL_NOT_FOUND', 'SQL bestand niet gevonden op %s');
define('_MD_AM_SQL_FOUND', 'SQL bestand gevonden op %s . <br  /> Aanmaak tabellen...');
define('_MD_SQL_NOT_VALID', 'is geen correcte SQL!');
define('_MD_AM_TABLE_CREATED', 'Tabel %s aangemaakt.');
define('_MD_AM_DATA_INSERT_SUCCESS', 'Gegevens weggeschreven in tabel %s.');
define('_MD_AM_RESERVED_TABLE', '%s is een gereserveerde tabel!');
define('_MD_AM_DATA_INSERT_FAIL', 'Kon %s niet toevoegen aan de databank.');
define('_MD_AM_CREATE_FAIL', 'FOUT: kan %s niet aanmaken');
define('_MD_AM_MOD_DATA_INSERT_SUCCESS', 'Module gegevens werden succesvol toegevoegd. Module ID: %s');
define('_MD_AM_BLOCK_UPDATED', 'Block %s updated. Block ID: %s.');
define('_MD_AM_BLOCK_CREATED', 'Block %s aangemaakt. Block ID: %s.');
define('_MD_AM_BLOCKS_ADDING', 'Blokken toevoegen...');
define('_MD_AM_BLOCKS_ADD_FAIL', 'FOUT: Kon blok %1$s niet aan de databank toevoegen! Databank fout: %2$s');
define('_MD_AM_BLOCK_ADDED', 'Blok %1$s toegevoegd. Block ID: %2$s');
define('_MD_AM_BLOCKS_DELETE', 'Blok verwijderen...');
define('_MD_AM_BLOCK_DELETE_FAIL', 'FOUT: Kon blok %1$s niet verwijderen. Blok ID: %2$s');
define('_MD_AM_BLOCK_DELETED', 'Blok %1$s is verwijderd. Blok ID: %2$s');
define('_MD_AM_BLOCK_TMPLT_DELETE_FAILED', 'FOUT: Kon template voor blok %1$s niet verwijderen van de databank. Template ID: %2$s');
define('_MD_AM_BLOCK_TMPLT_DELETED', 'Blok template %1$s verwijderd van de databank. Template ID: %2$s');
define('_MD_AM_BLOCK_ACCESS_FAIL', 'FOUT: Kon geen toegangsrechten voor blok toevoegen. Blok ID: %1$s  Groep ID: %2$s');
define('_MD_AM_BLOCK_ACCESS_ADDED', 'Blok toegangsrechten toegevoegd. Block ID: %1$s, Group ID: %2$s');
define('_MD_AM_CONFIG_ADDING', 'Configuratiedata voor de module toevoegen...');
define('_MD_AM_CONFIGOPTION_ADDED', 'Configuratie optie toegevoegd. Naam: %1$s Waarde: %2$s');
define('_MD_AM_CONFIG_ADDED', 'Configuratie %s toegevoegd aan de databank');
define('_MD_AM_CONFIG_ADD_FAIL', 'FOUT: kon configuratie %s niet toevoegen aan de databank.');
define('_MD_AM_PERMS_ADDING', 'Groepsrechten instellen...');
define('_MD_AM_ADMIN_PERM_ADD_FAIL', 'FOUT: Kon geen administratierechten toevoegen voor Groep ID %s');
define('_MD_AM_ADMIN_PERM_ADDED', 'Administratie rechten toegevoegd voor Groep ID %s');
define('_MD_AM_USER_PERM_ADD_FAIL', 'FOUT: Kon geen gebruiker toegangsrechten toevoegen voor Groep ID: %s');
define('_MD_AM_USER_PERM_ADDED', 'Gebruiker toegangsrechten toegevoegd voor Groep ID: %s');
define('_MD_AM_AUTOTASK_FAIL', 'FOUT: Kon Autotask niet toevoegen aan de databank. Naam: %s');
define('_MD_AM_AUTOTASK_ADDED', 'Taak toegevoegd aan de Autotask lijst.Taak Naam: %s');
define('_MD_AM_AUTOTASK_UPDATE', 'Update Autotasks...');
define('_MD_AM_AUTOTASKS_DELETE', 'Verwijderen Autotasks...');
define('_MD_AM_SYMLINKS_DELETE', 'Links verwijderen van de Symlink Manager...');
define('_MD_AM_SYMLINK_DELETE_FAIL', 'FOUT: Kon link %1$s niet verwijderen van de databank. Link ID: %2$s');
define('_MD_AM_SYMLINK_DELETED', 'Link %1$s verwijderd van de databank. Link ID: %2$s');
define('_MD_AM_DELETE_FAIL', 'FOUT: Kon %s niet verwijderen');
define('_MD_AM_MOD_UP_TEM', 'Bijwerken templates...');
define('_MD_AM_TEMPLATE_INSERT_FAIL', 'FOUT: Kon template %s niet aan de databank toevoegen');
define('_MD_AM_TEMPLATE_UPDATE_FAIL', 'FOUT: Kon template%s niet updaten.');
define('_MD_AM_TEMPLATE_INSERTED', 'Template %s toegevoegd aan de databank. (ID: %s)');
define('_MD_AM_TEMPLATE_COMPILE_FAIL', 'FOUT: template %s kon niet gecompileerd worden');
define('_MD_AM_TEMPLATE_COMPILED', 'Template %s gecompileerd.');
define('_MD_AM_TEMPLATE_RECOMPILED', 'Template %s ge-hercompileerd.');
define('_MD_AM_TEMPLATE_RECOMPILE_FAIL', 'FOUT: template %s kon niet ge-hercompileerd worden');
define('_MD_AM_TEMPLATES_ADDING', 'Templates toevoegen...');
define('_MD_AM_TEMPLATES_DELETE', 'Templates verwijderen...');
define('_MD_AM_TEMPLATE_DELETE_FAIL', 'FOUT: Kon template %1$s niet uit de databank verwijderen. Template ID: %2$s');
define('_MD_AM_TEMPLATE_DELETED', 'Template %1$s verwijderd uit de databank. Template ID: %2$s');
define('_MD_AM_TEMPLATE_UPDATED', 'Template %s geupdate.');
define('_MD_AM_MOD_TABLES_DELETE', 'Verwijderen van de module tabellen...');
define('_MD_AM_MOD_TABLE_DELETE_FAIL', 'FOUT: kon tabel %s niet verwijderen');
define('_MD_AM_MOD_TABLE_DELETED', 'Tabel %s verwijderd.');
define('_MD_AM_MOD_TABLE_DELETE_NOTALLOWED', 'FOUT: Niet toegestaan om tabel %s te verwijderen!');
define('_MD_AM_COMMENTS_DELETE', 'Verwijderen commentaren...');
define('_MD_AM_COMMENT_DELETE_FAIL', 'FOUT: Kon commentaren niet verwijderen');
define('_MD_AM_COMMENT_DELETED', 'Commentaren verwijderd');
define('_MD_AM_NOTIFICATIONS_DELETE', 'Meldingen verwijderen...');
define('_MD_AM_NOTIFICATION_DELETE_FAIL', 'FOUT: Kon meldingen niet verwijderen...');
define('_MD_AM_NOTIFICATION_DELETED', 'Meldingen verwijderd');
define('_MD_AM_GROUPPERM_DELETE', 'Groepsrechten verwijderen...');
define('_MD_AM_GROUPPERM_DELETE_FAIL', 'FOUT: Kon groepsrechten niet verwijderen');
define('_MD_AM_GROUPPERM_DELETED', 'Groepsrechten verwijderd');
define('_MD_AM_CONFIGOPTIONS_DELETE', 'Verwijderen module configuratie opties...');
define('_MD_AM_CONFIGOPTION_DELETE_FAIL', 'FOUT: Kon configuratie opties niet verwijderen van de databank. Configuratie ID: %s');
define('_MD_AM_CONFIGOPTION_DELETED', 'Configuratie gegevens verwijderd van de databank. Configuratie ID: %s');
