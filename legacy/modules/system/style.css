/* IMPORTANT, <PERSON><PERSON>AS<PERSON> RESPECT THIS SEMANTIC ORDER !!! :
	- position box,
	- width,
	- heigth,
	- margin,
	- background-color,
	- background-image,
	- padding,
	- content (color, style font, ...),
	- border,
	 ... */

/* General definitions */
html, body {background-color : #FFF; font-size: 12px; font-family: Verdana, Arial, Helvetica, sans-serif; margin: 0px; padding: 0px;
BACKGROUND: url(images/background.png) #fcfcfc repeat-x 0px 85px;
}

img { border: 0;}

FIELDSET
{
    BORDER-RIGHT: transparent 0px;
    PADDING-RIGHT: 1em;
    BORDER-TOP: #e8e8e8 1px solid;
    PADDING-LEFT: 1em;
    MARGIN-BOTTOM: 1em;
    PADDING-BOTTOM: 0.5em;
    BORDER-LEFT: transparent 0px;
    PADDING-TOP: 0.5em;
    BORDER-BOTTOM: transparent 0px
}
LEGEND
{
    PADDING-RIGHT: .5em;
    DISPLAY: block;
    PADDING-LEFT: .5em;
    FONT-WEIGHT: bold;
    FONT-SIZE: 105%;
    MARGIN: 0px;
}
LABEL
{
    DISPLAY: block
}

INPUT[type=text]
{
    PADDING-RIGHT: 0.25em;
    PADDING-LEFT: 0.25em;
    BACKGROUND: url(images/form-input.png) #fff no-repeat left top;
    PADDING-BOTTOM: 1px;
    MARGIN: 0.25em 0px;
    PADDING-TOP: 1px;
    BORDER: 1px solid #D0D0D0;
}
INPUT[type=password]
{
    PADDING-RIGHT: 0.25em;
    PADDING-LEFT: 0.25em;
    BACKGROUND: url(images/form-input.png) #fff no-repeat left top;
    PADDING-BOTTOM: 1px;
    MARGIN: 0.25em 0px;
    PADDING-TOP: 1px;
    BORDER: 1px solid #D0D0D0;
}
INPUT
{
}

SELECT
{

}
BUTTON, INPUT[type=submit] , INPUT[type=clear] , INPUT[type=button] , INPUT[type=reset]
{
    PADDING-RIGHT: 0.5em;
    PADDING-LEFT: 0.5em;
    PADDING-BOTTOM: 2px;
    PADDING-TOP: 2px;
    BACKGROUND-IMAGE: url(images/table-caption-grey.png);
    BACKGROUND-POSITION: bottom;
    BACKGROUND-REPEAT: repeat-x;
    COLOR: #444;
    FONT-WEIGHT: bold;
    CURSOR: pointer;
    BORDER: outset 1px #ccc;
}

hr { height: 3px; width: 95%; text-align:center; border: 3px #E18A00 solid;}

/* lists */
ul { margin: 2px; padding: 2px; list-style: decimal inside; text-align: left;}
li { margin-left: 2px; color: #000; list-style: square inside;}

/* titles */
h1, h2, h3, h4 {padding: 0px}
h1 { font-size: 1.4em;}
h2 { font-size: 1.2em;}
h3 { font-size: 1.1em;}
h4 { font-size: 1.1em; font-weight: bold;}

/* ===== hypertext links ===== */
a:link { background-color: transparent; color: #666; font-weight: bold; text-decoration: none;}
a:visited { background-color: transparent; color: #666; font-weight: bold; text-decoration: none;}
a:hover { background-color: transparent; color: #E18A00; font-weight: bold; text-decoration: none;}

/* ===== Code and Quote Definition ===== */
.xoopsCode { background-color: #FAFAFA; color: #006600; font-size: .9em; border: #c2cdd6 1px dashed;}
.icmsCodePHP { background-color: #FAFAFA; color: #006600; font-size: .9em; border: #c2cdd6 1px dashed;}
.icmsCodeCSS { background-color: #FAFAFA; color: #006600; font-size: .9em; border: #c2cdd6 1px dashed;}
.icmsCodeJS { background-color: #FAFAFA; color: #006600; font-size: .9em; border: #c2cdd6 1px dashed;}
.icmsCodeHTML { background-color: #FAFAFA; color: #006600; font-size: .9em; border: #c2cdd6 1px dashed;}
.xoopsQuote { background-color: #FAFAFA; color: #444; font-size: .9em; line-height: 1.2em; text-align: justify; border: #c2cdd6 1px dashed;}

/* ===== Links for Quotes ===== */
.xoopsQuote a:link, div.xoopsQuote a:visited { background-color: transparent; color: #444; font-weight: bold;}
.xoopsQuote a:hover, div.xoopsQuote a:active { background-color: transparent; color: #1778cb;}

/* ===== Redirect messages ===== */
.errorMsg,.confirmMsg, .resultMsg , .warningMsg, .successMsg { padding: .8em; text-align:center; margin-bottom: 1em; border: 2px solid #ddd;}
.errorMsg { background-color: #FBE3E4; color: #D12F19; border-color: #FBC2C4; }
.confirmMsg { background-color: #FFF6BF; color: #817134; border-color: #FFD324; }
.warningMsg { background-color: #99CCFF; color: #000099; border-color: #0099FF; }
.resultMsg { background-color: #E6EFC2; color: #529214; border-color: #C6D880; }
.errorMsg a { background-color: transparent; color: #D12F19; }
.confirmMsg a { background-color: transparent; color: #817134; }
.errorMsg { background-color: #FBE3E4; color: #D12F19; border-color: #FBC2C4; }
.successMsg { background-color: #9FFF8F; color: #143F22; border-color: #2F5F3F; }
.successMsg a { background-color: transparent; color: #529214; }

div .errorMsg a {text-decoration: underline;}
div .errorMsg a:hover {color: #000;}

/* ===== General small ===== */
.fontSmall { font-size : .7em;}
a.fontSmall { background-color: transparent; color: #006699;}
a.fontSmall:hover { background-color: transparent; color: #C23030; text-decoration: underline;}

/* ===== forms elements ===== */
input { background-color: #fff; border: #CCC 1px solid; font-size: .8em; font-family: verdana, arial, helvetica, sans-serif; }
input:focus { background-color: #FFFFBB; border: #000 1px solid;}
input:hover { border: #000 1px solid;}

textarea { background-color: #fff; border: #CCC 1px solid; width: 570px; font-size: .8em; font-family: verdana, arial, helvetica, sans-serif; }
textarea:focus { background-color: #FFFFBB; border: #000 1px solid; }
textarea:hover { border: #000 1px solid;}

select { background-color: #fff; border: #CCC 1px solid; font-size: .8em; font-family: verdana, arial, helvetica, sans-serif; }
select:focus { background-color: #FFFFBB; border: #000 1px solid;}
select:hover { border: #000 1px solid;}

input.formTextBox { background: #fff; color:inherit; font-size: .8em; font-family: verdana, arial, helvetica, sans-serif;border: #000 1px solid; }

/* ===== content module ===== */
.content { text-align: justify; padding: 0 15px 0px 15px;}

.xoopsCenter { text-align:center;}

.bg1 { background-color: #E6E6E6; color:inherit;}
.bg2 { background-color: #2F5376; color:inherit;}
.bg3 { background-color: #2F5376; color: #fff;}
.bg4 { background-color: #ECECEC; color:inherit;}
.bg5 { background-color: #ECECEC; color:inherit;}

/* ===== tables ===== */
caption {font-style: italic; font-weight: bold;}

th, thead { background:transparent url(images/bg-lr-titles.gif) repeat scroll 0 0; color:#666; border:1px solid #AAAAAA; padding:2px; vertical-align:middle; }
.head { background-color:#e3e3f3; color:inherit; border:1px solid #CCC;  padding:2px; font-weight:bold; }
.even { background-color:#FFFFFF; color:inherit; border: 1px solid #CCCCCC; padding:2px; }
.odd { background-color: #f1f4fb; color:inherit; border: 1px solid #CCCCCC; padding:2px; }
.foot, tfoot { background-color: #eeeeee; color:inherit; border: 1px solid #4e4e4e; padding: 2px; font-weight: bold; }
.outer {}

tr.head td { background-color:#FFFFDD; border:1px solid #F3AC03; color:inherit; font-weight:bold; padding:2px;}
tr.even td { background-color:#FFFFFF; color:inherit; border: 1px solid #CCCCCC; padding:2px; }
tr.odd td { background-color: #f1f4fb; color:inherit; border: 1px solid #CCCCCC; padding:2px; }
tr.foot td { background-color: #eeeeee; color:inherit; border: 1px solid #4e4e4e; padding: 2px; font-weight: bold; }

form td.head {width: 30%;}
/* ===== debug messages ===== */
#xo-logger-output {font-size: .8em;}
/**
 * Declaraciones para nuevo formato
 * <AUTHOR>
 */
div#logoHead{
	padding: 0;
	text-align: left;
	background: url(images/header-bg.png) repeat-x;
	border-bottom: 4px solid #ffb500;
}
div#logoHead span.left{
	float: left;
}
div#logoHead span.right{
	float: right;
}
div#navbarCP{background:url(images/bg-lr-titles.gif);font-weight:normal;font-family:Arial, Helvetica, sans-serif;height:25px;position:absolute;width:100%;z-index:10;clear:both;}
div#navbarCP ul {list-style:none;padding:0;margin:0;}
div#navbarCP ul li {list-style:none;padding:0;float:left;margin:0;margin-left:3px;position:relative;}
div#navbarCP ul li a {padding:4px;display:block;color:#4d4d4d;font-weight:normal;font-size:14px;padding-right:20px;}
div#navbarCP ul li a:hover {border-color:#ADD8E6;color:#000;text-decoration:none;}
div#navbarCP ul li ul {display:none;position:absolute;top:1em;margin-left:15px;background:#FFFFFF;border:1px solid #000000;padding:0;z-index:1;}
div#navbarCP ul li ul a {color:#666666;font-weight:normal;background:url(images/item.png) no-repeat left;padding-left:20px;margin:0;font-size:11px;border:1px solid #FFFFFF;}
div#navbarCP ul li ul a:hover {color:#000000;background-color:#E7F4F7;border-color:#ADD8E6;border:1px solid #ffffff;}
div#navbarCP ul li ul li {width:150px;margin:0;}
div#navbarCP ul li > ul {top:auto;left:auto;}
div#navbarCP ul li:hover > ul, div#navbarCP li.over ul {display:block;}
div#navbarCP ul li ul li {float: none;}
div#navbarCP ul li ul li ul {margin-left:150px;margin-top:-25px;}
div#navbarCP ul li ul li ul li {display:none;}
div#navbarCP ul li ul li:hover ul li {display:block;}

div#bodyCP{
	width: 95%;
	margin: 0 auto;
	background-color: #FFFFFF;
	border: 1px solid #CCCCCC;
	padding: 8px;
}
div#containBodyCP{
	padding: 0;
	margin: 0;
}
div#navOptionsCP{
	padding: 0px 5px 0px 5px;
	text-align: right;
	background: #EEEEEE;
	border-top: 1px solid #FFFFFF;
	border-bottom: 1px solid #CCCCCC;
	height: 35px;
	position: relative;
	top: 25px;
	clear: both;
}
div#navOptionsCP a{
	border: 1px solid #DFE0FD;
	height: 32px;
	display: block;
	width: 32px;
	float: right;
	margin: 0;
	margin-right: 2px;
}
div#navOptionsCP a:hover{
	background-color: #E7F4F7;
	border-color: #ADD8E6;
}
div#navOptionsCP .modname{
	float: left;
	font-size: 18px;
	font-weight: bold;
	color: Silver;
	margin-top: 4px;
}
.CPrssLinks{
	padding: 4px;
}
.CPrssLinks a{
	font-size: 12px;
	background: url(images/item.png) no-repeat left;
	padding: 2px;
	display: block;
	padding-left: 18px;
}
div.CPfoot{
	text-align: center;
	font-size: 10px;
	font-weight: bold;
	color: #666666;
	background: url(images/bg-lr-titles.gif);
	height: 15px;
	border-top: 1px solid #ddd;
	border-bottom: 1px solid #ddd;
}

.CPbigTitle{
	font-size: 20px;
	color: #777777;
	background: no-repeat left top;
	font-weight: bold;
	height: 40px;
	vertical-align: middle;
	padding: 10px 0 0 50px;
	border-bottom: 3px solid #000000;
}
#modlogo {
	float: left;
}
#nav-change-language {
	float: right;
	padding-top: 5px;
	padding-right: 5px;
}
/* === Modules Admin Menu === */

#buttontop {
	float:left;
	width:100%;
	background: #e7e7e7;
	line-height:normal;
	margin: 0;
}
#buttonbar {
	float:left;
	width:100%;
	background: #e7e7e7 url('images/bg.gif') repeat-x left bottom;
	line-height:normal;
}
#buttonbar ul {
	margin:0;
	margin-top: 15px;
	padding:10px 10px 0;
	list-style:none;
}
#buttonbar li {
	display:inline;
	margin:0;
	padding:0;
}
#buttonbar a {
	float:left;
	background:url('images/left_both.gif') no-repeat left top;
	margin:0;
	padding:0 0 0 9px;
	border-bottom:1px solid #000;
	text-decoration:none;
}
#buttonbar a span {
	float:left;
	display:block;
	background:url('images/right_both.gif') no-repeat right top;
	padding:5px 15px 4px 6px;
	font-weight:bold;
	color:#765;
}
/* Commented Backslash Hack hides rule from IE5-Mac \*/
#buttonbar a span {
	float:none;
}
/* End IE5-Mac hack */

#buttonbar a:hover span {
	color:#333;
}
#buttonbar #current a {
	background-position:0 -150px;
	border-width:0;
}
#buttonbar #current a span {
	background-position:100% -150px;
	padding-bottom:5px;
	color:#333;
}
#buttonbar a:hover {
	background-position:0% -150px;
}
#buttonbar a:hover span {
	background-position:100% -150px;
}
#submenuswrap {
	float: left;
	background: #EFEFEF;
	width: 100%;
	border-bottom: 1px solid #000000;
}
#submenus {
	padding: 4px 0px 4px 8px;
	float: left;
	font-size: 92%;
}
.subitem {
	float:left;
	padding-right: 10px;
}

#currentsubitem {
	float:left;
	padding-right: 10px;
	font-weight: bold;
}

#wrap {
	width:100%;
	border-top: 1px solid #000000;
	border-left: 1px solid #000000;
	border-right: 1px solid #000000;
	float: left;
	margin-bottom: 15px;
}

.clear {
	clear: both;
}
div#text_align{
	text-align: left;
}
div#floats_l{
	float:left;
}
div#floats_r{
	float:right;
}
/*image manager*/
.imanager_image_box {
    float:left;
    border:1px solid #CCCCCC;
    padding:3px;
    margin:5px;
    text-align:center;
    width:165px;
    overflow:hidden;
}

.imanager_image_box:hover {
    background-color: #FFF6BF;
}

.imanager_image_img {
    display:block;
    height:120px;
    overflow:hidden;
}

.imanager_image_img img{
    max-width:159px;
}

.imanager_image_label{
    display:block;
    font-size:13px;
    color: #E24949;
    font-weight:bold;
    line-height:180%;
}

.imanager_image_info{
    display:block;
    font-size:9px;
    font-weight:normal;
    color: #333333;
    line-height:120%;
    text-align:left;
}

.imanager_image_btns{
    display:block;
    margin-top:3px;
}

.img_list_info_panel{
    padding: 5px;
    margin:5px;
}

tr.blocked td {background-color: #FF0000;}

.imgcat_notwrite {background-color: #FF0000; width:20px; height: 17px; padding-top: 3px; overflow: visible; margin-bottom: 2px;}
.imgcat_notwrite span{margin-left: 25px; display: block; width: 400px;}

/* Tooltip box */
.caption-text img {float: right; padding-top: 2px;}
span.helptext {position: absolute; right: 50px; z-index: 9000; background-color:#000; outline:1px solid #669; border:2px solid #fff; padding:10px 15px; width:200px; display:none; color:#fff; text-align:left; font-size:12px; font-weight:normal;
/* outline radius for mozilla/firefox only */ 
outline-radius:4px; 
-moz-outline-radius:4px; 
-webkit-outline-radius:4px;
/* opacity for tooltip box */ 
filter:alpha(opacity=70);
-moz-opacity:0.75;
-khtml-opacity: 0.75;
opacity: 0.75;
}