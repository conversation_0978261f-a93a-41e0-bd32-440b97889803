<html xmlns="http://www.w3.org/1999/xhtml">
  <head>
    <script src="../../js/codemirror.js" type="text/javascript"></script>
    <title>CodeMirror: Lua demonstration</title>
  </head>
  <body style="padding: 20px;">

<p>This page demonstrates <a href="../../index.html">CodeMirror</a>'s
Lua parser. Written by <a href="http://francio.pl/"><PERSON><PERSON><PERSON></a>, released under a BSD-style <a
href="LICENSE">license</a>.</p>

<div style="border: 1px solid black; padding: 0px;">
<textarea id="code" cols="120" rows="30">
--[[
example useless code to show lua syntax highlighting
this is multiline comment
]]

function blahblahblah(x)

  local table = {
    "asd" = 123,
    "x" = 0.34,  
  }
  if x ~= 3 then
    print( x )
  elseif x == "string"
    my_custom_function( 0x34 )
  else
    unknown_function( "some string" )
  end

  --single line comment
  
end

function blablabla3()

  for k,v in ipairs( table ) do
    --abcde..
    y=[=[
  x=[[
      x is a multi line string
   ]]
  but its definition is iside a highest level string!
  ]=]
    print(" \"\" ")
  --this marks a parser error:
  s = [== asdasdasd]]

    s = math.sin( x )
  end

end
</textarea>
</div>

<script type="text/javascript">
  var editor = CodeMirror.fromTextArea('code', {
    height: "350px",
    parserfile: "../contrib/lua/js/parselua.js",
    stylesheet: "css/luacolors.css",
    path: "../../js/"
  });
</script>
  </body>
</html>
