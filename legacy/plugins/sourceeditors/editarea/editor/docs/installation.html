<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="en" >
<head>
	<title>EditArea documentation</title>
	<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
	<link href="doc_style.css" rel="stylesheet" type="text/css" />
</head>
<body>
	<div class='header'>
		<h1>Installation instructions</h1>
	</div>
	<div class='content'>
		<p>Installing EditArea is very simple, follow the instructions here. Also look at the 
			<a href="configuration.html">options for configuration</a> and the different 
			<a href='include.html'>javascript load possibility</a>.
		</p>
		<div class="separator"></div>

		<h2>Requirements</h2>
		<p>EditArea has no direct requirements except for <a href="compatiblity.html">browser
			compatibility</a> and of course JavaScript needs to be turned on. 
			For developpers there is also a PHP compressor that is included in the release.
		</p>
		<div class="separator"></div>
		
		<h2>Downloading</h2>
		<p>For downloading check the <a href="https://sourceforge.net/projects/editarea/"> 
			sourceforge web site.</a>
		</p>
		<div class="separator"></div>
		
		<h2>Extracting the archives</h2>
		<p>On windows you could use <a href="http://www.winzip.com">WinZip</a> or something similar. 
			And on other operating systems such as Linux you simply extract the archive with 
			the tar command.
		</p>
		<p>
			You should extract EditArea somewhere in your website. Notice that EditArea loads additionnal files 
			while being used (language translation, syntax definition and images), so don't delete any of the 
			file in the archive, and be sure that any files can be accessed.
		</p>
		<p>
			If you plan to use &quot;edit_area_compressor.php&quot; be sure that PHP scripts are allowed 
			to write in editarea repertory (at the same level than the file &quot;edit_area_compressor.php&quot;) 
			in order to allow disk caching.
		</p>
		<div class="separator"></div>

		<h2>Making changes on your web site</h2>
		<p>Once you have extracted the archive you will need to edit the pages 
			to include the configuration and javascript for EditArea. 
			Please note that you should probably only include the EditArea javascript on the pages 
			that need it, not all the pages of the web site. Remember to change the URL to
			the .js below to match your installation path.</p>
		<p>
		<h3>The most basic page integration (converts one textarea into editor):</h3>
		<p>
<pre>
&lt;html&gt;
&lt;head&gt;
&lt;title&gt;EditArea Test&lt;/title&gt;
<strong>&lt;script language=&quot;javascript&quot; type=&quot;text/javascript&quot; src=&quot;/editarea/edit_area/edit_area_full.js&quot;&gt;&lt;/script&gt;
&lt;script language=&quot;javascript&quot; type=&quot;text/javascript&quot;&gt;
editAreaLoader.init({
	id : &quot;textarea_1&quot;		// textarea id
	,syntax: "css"			// syntax to be uses for highgliting
	,start_highlight: true		// to display with highlight mode on start-up
});
&lt;/script&gt;</strong>
&lt;/head&gt;
&lt;body&gt;
&lt;form method=&quot;post&quot;&gt;
	&lt;textarea id=&quot;textarea_1&quot; name=&quot;content&quot; cols=&quot;80&quot; rows=&quot;15&quot;&gt;
/*This is some css that will be editable with EditArea.*/
body, html{
	margin: 0; 
	padding: 0;
	height: 100%;
	border: none;
	overflow: hidden;
}&lt;/textarea&gt;

&lt;/form&gt;
&lt;/body&gt;
&lt;/html&gt;
</pre>
		</p>
		<p>
			See the <a href='configuration.html'>configuration help</a> to learn about 
			initialization options, 
			and the <a href='include.html'>include help</a> to learn more about the way 
			to use the best script include (there is 4 possible files for EditArea loading).
		</p>
		<p>
			Here is an example of EditArea possibilities: <a href="../exemples/exemple_full.html">Full exemple</a>.  
		</p>
		<p>
			Just be sure you've read the documentation before...
		</p>
	</div>
	<div class='footer'>
		<div class="indexlink"><a href="index.html">Index</a></div>	
		<div class='copyright'>EditArea - &copy; Christophe Dolivet 2007-2008</div>
		<br style="clear: both" />
	</div>
</body>
</html>
