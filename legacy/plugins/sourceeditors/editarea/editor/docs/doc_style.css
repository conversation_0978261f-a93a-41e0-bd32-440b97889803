body {
	background-color: #FFFFFF;
	font-family: Verdana, Arial, helvetica, sans-serif;
	font-size: 12px;
}

h1{
	font-size: 18px;
	font-weight: bold;
	padding: 0;
	margin: 4px;
}

h2 {
	font-size: 14px;
	font-weight: bold;
	padding: 0;
	margin: 0;
	margin-top: 4px;
	margin-bottom: 4px;
}

h3 {
	font-size: 11px;
	font-weight: bold;
	padding: 0;
	margin: 0;
	margin-bottom: 3px;
}

h4, h5, h6{
	margin: 0;
	padding: 0;
}

pre, code{
	margin: 0;
	padding: 0 5px;
	background-color: #E6EBF1;
}


a:hover{
	color: #666666;
	text-decoration: underline;
}

a{
	color: #666666;
	text-decoration: underline;
}

ul, ol{
	padding: 0px;
	margin: 3px 0 3px 10px;
}

li{
	padding: 0;
	margin: 3px 0 3px 10px;
}

li li{
	padding: 0;
	margin-left: 30px;
}

table{
	border-collapse: collapse;	
}

td, th{
	padding: 4px;
	border: 2px groove #000000;
}

thead {
	background-color: #E6EBF1;	
}

.header{
	border: #E0E0E0 solid 1px;
}

.footer{
	border: #E0E0E0 solid 1px;	
	height: 1.3em;
	padding: 2px;
}

.content{
	padding: 10px 0 10px 10px;
}

.indexlink{
	float: right;
}

.separator {
	border-bottom: 1px solid #E6EBF1;
	margin-top: 10px;
	margin-bottom: 10px;
}


.optionlist li li{
	text-indent: -15px;
	padding-left: 15px;
	line-height: 1.3em;
}

.optionlist .underline{
	background-color: #E6EBF1;
	border-bottom: dashed #000000 1px; 
}

.marked{
	color: #FF0000;
	font-weight: bold;
}
