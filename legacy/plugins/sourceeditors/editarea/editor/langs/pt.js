editAreaLoader.lang["pt"]={
new_document: "Novo documento",
search_button: "Localizar e substituir",
search_command: "Localizar próximo",
search: "Localizar",
replace: "Substituir",
replace_command: "Substituir",
find_next: "Localizar",
replace_all: "Subst. tudo",
reg_exp: "Expressões regulares",
match_case: "Diferenciar maiúsculas e minúsculas",
not_found: "Não encontrado.",
occurrence_replaced: "Ocorrências substituidas",
search_field_empty: "Campo localizar vazio.",
restart_search_at_begin: "Fim das ocorrências. Recomeçar do inicio.",
move_popup: "Mover janela",
font_size: "--<PERSON><PERSON><PERSON> da fonte--",
go_to_line: "Ir para linha",
go_to_line_prompt: "Ir para a linha:",
undo: "Desfazer",
redo: "<PERSON><PERSON><PERSON>",
change_smooth_selection: "Opções visuais",
highlight: "Cores de sintaxe",
reset_highlight: "Resetar cores (se não sincronizado)",
word_wrap: "toggle word wrapping mode",
help: "Sobre",
save: "Salvar",
load: "Carregar",
line_abbr: "Ln",
char_abbr: "Ch",
position: "Posição",
total: "Total",
close_popup: "Fechar",
shortcuts: "Shortcuts",
add_tab: "Adicionar tabulação",
remove_tab: "Remover tabulação",
about_notice: "Atenção: Cores de sintaxe são indicados somente para textos pequenos",
toggle: "Exibir editor",
accesskey: "Accesskey",
tab: "Tab",
shift: "Shift",
ctrl: "Ctrl",
esc: "Esc",
processing: "Processando...",
fullscreen: "fullscreen",
syntax_selection: "--Syntax--",
close_tab: "Close file"
};
