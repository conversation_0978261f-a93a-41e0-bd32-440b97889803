@import "cssparsed.css";

html,body {
font:.8em Verdana,Helvetica,sans-serif;
background:#f8f8f6
}

code {
font-size:1.2em
}

div#rightcol {
padding-left:32em
}

fieldset {
display:block;
margin:.5em 0;
padding:1em;
border:solid #7284ab 2px
}

fieldset.code_output {
display:inline
}

h1 {
font-size:2em
}

small {
font-size:.7em
}

fieldset#field_input {
float:left;
margin:0 .5em 1em 0
}

fieldset#options,fieldset#code_layout {
width:31em
}

input#submit {
clear:both;
display:block;
margin:1em
}

select {
margin:2px 0 0
}

label.block {
display:block
}

legend {
background:#c4e1c3;
padding:2px 4px;
border:dashed 1px
}

textarea#css_text {
width:27em;
height:370px;
display:block;
margin-right:1em
}

.help {
cursor:help
}

p.important {
border:solid 1px red;
font-weight:700;
padding:1em;
background:#fff
}

p {
margin:1em 0
}

dl {
padding-left:.5em
}

dt {
font-weight:700;
margin:0;
float:left;
clear:both;
height:1.5em
}

dd {
margin:0 0 0 4em;
height:1.5em
}

fieldset#messages {
background:#fff;
padding:0 0 0 1em
}

fieldset#messages div {
height:10em;
overflow:auto
}

dd.Warning {
color:orange
}

dd.Information {
color:green
}