<?php
/**
 * Simple test file to verify Laravel-ImpressCMS integration
 */

echo "<h1>ImpressCMS Legacy Integration Test</h1>";
echo "<p>This file is served from the legacy folder through Laravel.</p>";
echo "<p>Current time: " . date('Y-m-d H:i:s') . "</p>";
echo "<p>PHP Version: " . PHP_VERSION . "</p>";
echo "<p>Server: " . $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown' . "</p>";
echo "<p>Request URI: " . $_SERVER['REQUEST_URI'] . "</p>";
echo "<p>Script Name: " . $_SERVER['SCRIPT_NAME'] . "</p>";

// Test $_GET parameters
if (!empty($_GET)) {
    echo "<h2>GET Parameters:</h2>";
    echo "<pre>" . print_r($_GET, true) . "</pre>";
}

// Test $_POST parameters
if (!empty($_POST)) {
    echo "<h2>POST Parameters:</h2>";
    echo "<pre>" . print_r($_POST, true) . "</pre>";
}

echo "<hr>";
echo "<p><a href='/'>Back to Laravel Home</a> | <a href='/test.php?param=test'>Test with GET parameter</a></p>";
?>
