<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ImpressCMS + Laravel Integration</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .links {
            text-align: center;
            margin-top: 30px;
        }
        .links a {
            display: inline-block;
            margin: 10px;
            padding: 10px 20px;
            background-color: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 5px;
        }
        .links a:hover {
            background-color: #0056b3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎉 ImpressCMS + Laravel Integration Successful!</h1>
        
        <div class="success">
            <strong>Integration Status:</strong> ✅ Working correctly!<br>
            This page is served from the <code>/legacy/</code> folder through Laravel's routing system.
        </div>
        
        <div class="info">
            <h3>How it works:</h3>
            <ul>
                <li>Laravel checks if a requested file exists in the <code>/legacy/</code> folder</li>
                <li>If the file exists, it's served through the Legacy Controller</li>
                <li>If the file doesn't exist, Laravel's normal routing takes over</li>
                <li>This allows seamless integration between ImpressCMS and Laravel</li>
            </ul>
        </div>
        
        <div class="info">
            <h3>System Information:</h3>
            <ul>
                <li><strong>Current Time:</strong> <?php echo date('Y-m-d H:i:s'); ?></li>
                <li><strong>PHP Version:</strong> <?php echo PHP_VERSION; ?></li>
                <li><strong>Request URI:</strong> <?php echo $_SERVER['REQUEST_URI']; ?></li>
                <li><strong>Server:</strong> <?php echo $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown'; ?></li>
            </ul>
        </div>
        
        <div class="links">
            <a href="/test.php">Test Legacy File</a>
            <a href="/laravel-test">Test Laravel Route</a>
            <a href="/user.php">Test ImpressCMS User Page</a>
            <a href="/admin.php">Test ImpressCMS Admin</a>
        </div>
        
        <div class="info">
            <h3>Next Steps:</h3>
            <ol>
                <li>Install and configure ImpressCMS database</li>
                <li>Update ImpressCMS configuration for the new environment</li>
                <li>Test all ImpressCMS functionality</li>
                <li>Gradually migrate features to Laravel as needed</li>
            </ol>
        </div>
    </div>
</body>
</html>
