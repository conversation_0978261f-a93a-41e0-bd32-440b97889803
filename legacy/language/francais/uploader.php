<?php
//traduction CPascalWeb
define("_ER_UP_MIMETYPELOAD", "Erreur lors du chargement des d&eacute;finitions des types mime");
define("_ER_UP_FILENOTFOUND", "Fichier non trouv&eacute;");
define("_ER_UP_INVALIDFILESIZE", "Taille du fichier invalide");
define("_ER_UP_FILENAMEEMPTY", "Le nom de fichier est vide");
define("_ER_UP_NOFILEUPLOADED", "Aucun fichier n'a &eacute;t&eacute; t&eacute;l&eacute;charger");
define("_ER_UP_ERROROCCURRED", "Une erreur s'est produite: Erreur #%s");
define("_ER_UP_UPLOADDIRNOTSET", "Destination du t&eacute;l&eacute;chargement non d&eacute;fini");
define("_ER_UP_FAILEDOPENDIR", "Erreur pour ouvrir le dossier: %s");
define("_ER_UP_FAILEDOPENDIRWRITE", "Erreur d'ouverture du dossier avec les permissions d'&eacute;criture: %s");
define("_ER_UP_FILESIZETOOLARGE", "Taille du fichier trop grande (Maximum %u bytes): %u bytes");
define("_ER_UP_FILEWIDTHTOOLARGE", "Largeur du fichier trop grande (Maximum %u px): %u px");
define("_ER_UP_FILEHEIGHTTOOLARGE", "Hauteur du fichier trop grande (Maximum %u px): %u px");
define("_ER_UP_MIMETYPENOTALLOWED", "Type MIME non autoris&eacute;: %s");
define("_ER_UP_FAILEDFETCHIMAGESIZE", "Echec lors de la r&eacute;cup&eacute;ration de la taille de l'image de %s, sauter le contr&ocirc;le de dimension maximum.. ");
define("_ER_UP_UNKNOWNFILETYPEREJECTED", "Rejet de ce type de fichier inconnu");
define("_ER_UP_ERRORSRETURNED", "Retours erreurs pendant le t&eacute;l&eacute;chargement");
define("_ER_UP_INVALIDIMAGEFILE", "Fichier image invalide");
define("_ER_UP_SUSPICIOUSREFUSED", "Refus de t&eacute;l&eacute;charger cette image suspecte");
define("_ER_UP_INVALIDFILENAME", "Nom de fichier invalide");
define("_ER_UP_FAILEDSAVEFILE", "Echec pour sauvegarder le fichier dans %s");
?>