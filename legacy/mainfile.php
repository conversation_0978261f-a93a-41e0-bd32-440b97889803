<?php
/**
* All information in order to connect to database are going through here.
*
* Be careful if you are changing data's in this file.
*
* @copyright	http://www.xoops.org/ The XOOPS Project
* @copyright	http://www.impresscms.org/ The ImpressCMS Project
* @license		http://www.gnu.org/licenses/old-licenses/gpl-2.0.html GNU General Public License (GPL)
* @since		XOOPS
* @package		Core
*/

if (!defined("XOOPS_MAINFILE_INCLUDED")) {
	define("XOOPS_MAINFILE_INCLUDED",1);

	// XOOPS Physical Path
	// Physical path to your main XOOPS directory WITHOUT trailing slash
	define('XOOPS_ROOT_PATH', dirname(__FILE__));

	// XOOPS Security Physical Path
	// Physical path to your security XOOPS directory WITHOUT trailing slash.
	// Ideally off your server WEB folder
	define('XOOPS_TRUST_PATH', dirname(__FILE__));

	// XOOPS Virtual Path (URL)
	// Virtual path to your main XOOPS directory WITHOUT trailing slash
	// Get the base URL from Laravel environment or construct it
	$protocol = (!empty($_SERVER['HTTPS']) && $_SERVER['HTTPS'] !== 'off') ? 'https://' : 'http://';
	$host = $_SERVER['HTTP_HOST'] ?? 'localhost';
	define('XOOPS_URL', $protocol . $host . '/legacy');

	define('XOOPS_CHECK_PATH', 0);

	// Database Configuration - Use Laravel's database configuration
	define('XOOPS_DB_TYPE', 'pdo.mysql');
	define('XOOPS_DB_CHARSET', 'utf8');
	define('XOOPS_DB_PREFIX', 'icms_');

	// Get database configuration from Laravel .env file
	define('XOOPS_DB_HOST', $_ENV['DB_HOST'] ?? 'localhost');
	define('XOOPS_DB_USER', $_ENV['DB_USERNAME'] ?? '');
	define('XOOPS_DB_PASS', $_ENV['DB_PASSWORD'] ?? '');
	define('XOOPS_DB_NAME', $_ENV['DB_DATABASE'] ?? '');

	// Password Salt Key
	define('XOOPS_DB_SALT', $_ENV['APP_KEY'] ?? 'default_salt_key');

	// Use persistent connection? (Yes=1 No=0)
	define('XOOPS_DB_PCONNECT', 0);

	// (optional) Physical path to script that logs database queries.
	define('ICMS_LOGGING_HOOK', '');

	define("XOOPS_GROUP_ADMIN", "1");
	define("XOOPS_GROUP_USERS", "2");
	define("XOOPS_GROUP_ANONYMOUS", "3");

    // Security check for bad globals
    foreach ( array('GLOBALS', '_SESSION', 'HTTP_SESSION_VARS', '_GET', 'HTTP_GET_VARS', '_POST', 'HTTP_POST_VARS', '_COOKIE', 'HTTP_COOKIE_VARS', '_REQUEST', '_SERVER', 'HTTP_SERVER_VARS', '_ENV', 'HTTP_ENV_VARS', '_FILES', 'HTTP_POST_FILES', 'xoopsDB', 'xoopsUser', 'xoopsUserId', 'xoopsUserGroups', 'xoopsUserIsAdmin', 'icmsConfig', 'xoopsOption', 'xoopsModule', 'xoopsModuleConfig', 'xoopsRequestUri') as $bad_global) {
        if (isset( $_REQUEST[$bad_global] )) {
            header( 'Location: '.XOOPS_URL.'/' );
            exit();
        }
    }

	define('ICMS_GROUP_ADMIN', XOOPS_GROUP_ADMIN);
	define('ICMS_GROUP_USERS', XOOPS_GROUP_USERS);
	define('ICMS_GROUP_ANONYMOUS', XOOPS_GROUP_ANONYMOUS);
	define( 'ICMS_URL', XOOPS_URL );
	define( 'ICMS_TRUST_PATH', XOOPS_TRUST_PATH );
	define( 'ICMS_ROOT_PATH', XOOPS_ROOT_PATH );

	if (!isset($xoopsOption['nocommon']) && XOOPS_ROOT_PATH != '') {
		include XOOPS_ROOT_PATH."/include/common.php";
	}
}
