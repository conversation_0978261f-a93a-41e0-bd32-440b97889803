<?php
/**
 * Interface for ImpressCMS core handlers.
 *
 * Defines the contract for handler classes that provide data access mechanisms
 * for their corresponding data objects.
 *
 * @category    ICMS
 * @package     Core
 * <AUTHOR> <on<PERSON><PERSON>@xoops.org>
 * @copyright   (c) 2000-2003 XOOPS.org
 * @license     http://www.gnu.org/licenses/old-licenses/gpl-2.0.html GNU General Public License (GPL)
 */

interface icms_core_Handler {
	/**
	 * Creates a new object.
	 *
	 * @return object
	 */
	public function create();

	/**
	 * Gets a value object.
	 *
	 * @param int $int_id
	 * @return object
	 */
	public function get($int_id);

	/**
	 * Insert or update object.
	 *
	 * @param object $object
	 * @return mixed
	 */
	public function insert($object);

	/**
	 * Delete object from database.
	 *
	 * @param object $object
	 * @return mixed
	 */
	public function delete($object);
}